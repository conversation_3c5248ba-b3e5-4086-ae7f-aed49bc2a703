# Use an official Node.js runtime with Python support as the base image
FROM node:20-alpine

# Install Python and pip for Python plugin support
RUN apk add --no-cache python3 py3-pip python3-dev build-base

# Create symbolic link for python command
RUN ln -sf python3 /usr/bin/python

# Set the working directory in the container
WORKDIR /usr/src/app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./
COPY shared/package*.json ./shared/
COPY errorhandler/package*.json ./errorhandler/
COPY marketplace/package*.json ./marketplace/
COPY services/capabilitiesmanager/package*.json ./services/capabilitiesmanager/

# Install the application dependencies
RUN npm install

# Copy the application code to the working directory
COPY shared ./shared
COPY errorhandler ./errorhandler
COPY marketplace ./marketplace
COPY services/capabilitiesmanager ./services/capabilitiesmanager

# Copy Python plugin framework files
COPY templates ./templates
COPY examples ./examples
COPY tools ./tools

# Build the TypeScript code
RUN cd shared && npm run build
RUN cd errorhandler && npm run build
RUN cd marketplace && npm run build
RUN npm run build --workspace=services/capabilitiesmanager

# Copy the local plugins from the source 'src/plugins' to the build output 'dist/plugins'
# The source path is relative to the build context root (/usr/src/app)
COPY services/capabilitiesmanager/src/plugins ./services/capabilitiesmanager/dist/plugins/

# Install Python dependencies for example plugins
RUN if [ -f examples/python-plugins/WEATHER/requirements.txt ]; then \
    python3 -m pip install --user -r examples/python-plugins/WEATHER/requirements.txt; \
    fi
RUN if [ -f examples/python-plugins/TEXT_ANALYSIS/requirements.txt ]; then \
    python3 -m pip install --user -r examples/python-plugins/TEXT_ANALYSIS/requirements.txt; \
    fi

# Make Python CLI tool executable
RUN chmod +x tools/python-plugin-cli.py

# Set the working directory to the service
WORKDIR /usr/src/app/services/capabilitiesmanager

# Expose the port the app runs on
EXPOSE 5060

# Define the command to run the application
CMD ["node", "--experimental-vm-modules", "dist/index.js"]