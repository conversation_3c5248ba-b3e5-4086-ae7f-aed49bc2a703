{"compilerOptions": {"target": "es2020", "module": "commonjs", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "sourceMap": true, "allowJs": true, "importHelpers": true, "baseUrl": ".", "paths": {"@cktmcs/shared": ["../../shared/dist"], "@cktmcs/shared/*": ["../../shared/dist/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "**/*.test.ts"]}