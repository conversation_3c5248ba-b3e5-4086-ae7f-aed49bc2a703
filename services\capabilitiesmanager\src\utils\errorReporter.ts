import { v4 as uuidv4 } from 'uuid';

// Define Error Severity Enum
export enum ErrorSeverity {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL',
}

// Define initial Planning Error Codes (can be expanded by other modules)
export const GlobalErrorCodes = {
  LLM_RESPONSE_PARSE_FAILED: 'P001_LLM_RESPONSE_PARSE_FAILED',
  BRAIN_QUERY_FAILED: 'P002_BRAIN_QUERY_FAILED',
  NO_GOAL_PROVIDED: 'P003_NO_GOAL_PROVIDED',
  INVALID_BRAIN_RESPONSE_FORMAT: 'P004_INVALID_BRAIN_RESPONSE_FORMAT',
  PLAN_VALIDATION_EMPTY_PLAN: 'P005_PLAN_VALIDATION_EMPTY_PLAN',
  PLAN_VALIDATION_DUPLICATE_OUTPUT_NAME: 'P006_PLAN_VALIDATION_DUPLICATE_OUTPUT_NAME',
  PLAN_VALIDATION_OUTPUT_NAME_CONVENTION_FAILED: 'P007_PLAN_VALIDATION_OUTPUT_NAME_CONVENTION_FAILED',
  PLAN_VALIDATION_MISSING_OR_FORWARD_REF_INPUT: 'P008_PLAN_VALIDATION_MISSING_OR_FORWARD_REF_INPUT',
  PLAN_VALIDATION_INVALID_STEP_STRUCTURE: 'P009_PLAN_VALIDATION_INVALID_STEP_STRUCTURE',
  
  GITHUB_AUTH_FAILED: 'G001_GITHUB_AUTH_FAILED',
  GITHUB_FORBIDDEN: 'G002_GITHUB_FORBIDDEN',
  GITHUB_RESOURCE_NOT_FOUND: 'G003_GITHUB_RESOURCE_NOT_FOUND',
  GITHUB_API_RATE_LIMIT: 'G004_GITHUB_API_RATE_LIMIT',
  GITHUB_SERVER_ERROR: 'G005_GITHUB_SERVER_ERROR',
  GITHUB_API_ERROR: 'G006_GITHUB_API_ERROR', // Generic GitHub API error
  GITHUB_CONFIG_ERROR: 'G007_GITHUB_CONFIG_ERROR',
  GITHUB_FETCH_DEFAULT_BRANCH_FAILED: 'G008_GITHUB_FETCH_DEFAULT_BRANCH_FAILED',

  PLUGIN_REGISTRY_INVALID_MANIFEST_PACKAGE_SOURCE: 'PR001_INVALID_MANIFEST_PACKAGE_SOURCE',
  PLUGIN_REGISTRY_GIT_CLONE_FAILED: 'PR002_GIT_CLONE_FAILED',
  PLUGIN_REGISTRY_GIT_CHECKOUT_FAILED: 'PR003_GIT_CHECKOUT_FAILED',
  PLUGIN_REGISTRY_GIT_FETCH_FAILED: 'PR004_GIT_FETCH_FAILED',
  PLUGIN_REGISTRY_GIT_RESET_FAILED: 'PR005_GIT_RESET_FAILED',
  PLUGIN_REGISTRY_DIR_CLEANUP_FAILED: 'PR007_DIR_CLEANUP_FAILED',
  PLUGIN_REGISTRY_CACHE_DIR_CREATION_FAILED: 'PR008_CACHE_DIR_CREATION_FAILED',
  PLUGIN_REGISTRY_UNKNOWN_GIT_ERROR: 'PR009_UNKNOWN_GIT_ERROR',
  PLUGIN_REGISTRY_MANIFEST_READ_FAILED: 'PR010_MANIFEST_READ_FAILED',

  CAPABILITIES_MANAGER_PLUGIN_PREPARATION_FAILED: 'CM001_PLUGIN_PREPARATION_FAILED',
  CAPABILITIES_MANAGER_PLUGIN_EXECUTION_FAILED: 'CM002_PLUGIN_EXECUTION_FAILED',
  CAPABILITIES_MANAGER_MANIFEST_VALIDATION_FAILED: 'CM003_MANIFEST_VALIDATION_FAILED',
  CAPABILITIES_MANAGER_ERROR_TRANSLATION_FAILED: 'CM004_ERROR_TRANSLATION_FAILED',
  PLUGIN_NOT_FOUND: 'CM005_PLUGIN_NOT_FOUND',
  PLUGIN_VERSION_NOT_FOUND: 'CM006_PLUGIN_VERSION_NOT_FOUND',
  INPUT_VALIDATION_FAILED: 'CM007_INPUT_VALIDATION_FAILED',
  PLUGIN_PERMISSION_VALIDATION_FAILED: 'CM008_PLUGIN_PERMISSION_VALIDATION_FAILED',
  ACCOMPLISH_PLUGIN_MANIFEST_NOT_FOUND: 'CM009_ACCOMPLISH_PLUGIN_MANIFEST_NOT_FOUND',
  ACCOMPLISH_PLUGIN_EXECUTION_FAILED: 'CM010_ACCOMPLISH_PLUGIN_EXECUTION_FAILED',
  CAPABILITIES_MANAGER_INVALID_REQUEST_GENERIC: 'CM011_INVALID_REQUEST_GENERIC',
  CAPABILITIES_MANAGER_UNKNOWN_VERB_HANDLING_FAILED: 'CM012_UNKNOWN_VERB_HANDLING_FAILED',
  CAPABILITIES_MANAGER_PLUGIN_STORE_FAILED: 'CM013_PLUGIN_STORE_FAILED',
  UNSUPPORTED_LANGUAGE: 'CM014_UNSUPPORTED_LANGUAGE',
  INTERNAL_ERROR_CM: 'CM015_INTERNAL_ERROR_CM',
  CAPABILITIES_MANAGER_PLUGIN_FETCH_FAILED: 'CM016_PLUGIN_FETCH_FAILED',
  CAPABILITIES_MANAGER_PLUGIN_INSTALLATION_FAILED: 'CM017_PLUGIN_INSTALLATION_FAILED',

  ARTIFACT_FILE_READ_FAILED: 'AS001_FILE_READ_FAILED',
  ARTIFACT_FILE_WRITE_FAILED: 'AS002_FILE_WRITE_FAILED',
  ARTIFACT_FILE_DELETE_FAILED: 'AS003_FILE_DELETE_FAILED',
  ARTIFACT_FILE_UPLOAD_FAILED: 'AS004_FILE_UPLOAD_FAILED',
  ARTIFACT_FILE_DOWNLOAD_FAILED: 'AS005_FILE_DOWNLOAD_FAILED',
  ARTIFACT_FILE_METADATA_READ_FAILED: 'AS006_FILE_METADATA_READ_FAILED',
  ARTIFACT_FILE_METADATA_WRITE_FAILED: 'AS007_FILE_METADATA_WRITE_FAILED',
  ARTIFACT_FILE_METADATA_DELETE_FAILED: 'AS008_FILE_METADATA_DELETE_FAILED',
  ARTIFACT_FILE_METADATA_UPLOAD_FAILED: 'AS009_FILE_METADATA_UPLOAD_FAILED',
  ARTIFACT_FILE_METADATA_DOWNLOAD_FAILED: 'AS010_FILE_METADATA_DOWNLOAD_FAILED',
  ARTIFACT_FILE_METADATA_UPDATE_FAILED: 'AS011_FILE_METADATA_UPDATE_FAILED',
  ARTIFACT_STORAGE_CONFIG_ERROR: 'AS012_STORAGE_CONFIG_ERROR',
  ARTIFACT_STORAGE_INTERNAL_ERROR: 'AS013_STORAGE_INTERNAL_ERROR',
  ARTIFACT_STORAGE_MKDIR_FAILED: 'AS014_MKDIR_FAILED',
  ARTIFACT_FILE_NOT_FOUND_DESPITE_METADATA: 'AS015_FILE_NOT_FOUND_DESPITE_METADATA',
  ARTIFACT_STORAGE_UPLOAD_FAILED: 'AS016_UPLOAD_FAILED',
  ARTIFACT_ID_INVALID_FORMAT: 'AS017_INVALID_ID_FORMAT',
  ARTIFACT_METADATA_PARSE_FAILED: 'AS018_METADATA_PARSE_FAILED',

  FILE_READ_ERROR: 'AS016_FILE_READ_FAILED',
  UNKNOWN_ERROR: 'U999_UNKNOWN_ERROR',
};

export interface StructuredErrorParams {
  error_code: string;
  severity: ErrorSeverity;
  message: string;
  source_component: string;
  contextual_info?: Record<string, any>;
  suggested_action?: string;
  original_error?: Error | any; 
  trace_id?: string;
  trace_id_param?: string; // For backward compatibility
}

export interface StructuredError {
  error_id: string;
  trace_id: string;
  timestamp_utc: string;
  error_code: string;
  severity: ErrorSeverity;
  message_human_readable: string;
  source_component: string;
  contextual_info: Record<string, any>;
  suggested_action?: string;
}

export function generateStructuredError({
  error_code,
  severity,
  message,
  source_component,
  contextual_info = {},
  suggested_action,
  original_error,
  trace_id,
  trace_id_param, // For backward compatibility
}: StructuredErrorParams): StructuredError {
  const error_id = uuidv4();
  const timestamp_utc = new Date().toISOString();
  const current_trace_id = trace_id || uuidv4();

  const enriched_contextual_info = { ...contextual_info };

  if (original_error) {
    if (original_error instanceof Error) {
      enriched_contextual_info.original_error_message = original_error.message;
      enriched_contextual_info.original_error_stack = original_error.stack;
    } else {
      enriched_contextual_info.original_error_details = String(original_error);
    }
  }

  const structuredErrorOutput: StructuredError = {
    error_id,
    trace_id: current_trace_id,
    timestamp_utc,
    error_code,
    severity,
    message_human_readable: message,
    source_component,
    contextual_info: enriched_contextual_info,
  };

  if (suggested_action) {
    structuredErrorOutput.suggested_action = suggested_action;
  }
  
  console.error(`StructuredError Generated [${source_component}]: ${message} (Code: ${error_code}, Trace: ${current_trace_id}, ID: ${error_id})`);

  return structuredErrorOutput;
}