FROM node:20-alpine

WORKDIR /usr/src/app

# Copy package.json files
COPY package*.json ./
COPY shared/package*.json ./shared/
COPY errorhandler/package*.json ./errorhandler/
COPY services/configservice/package*.json ./services/configservice/

# Install dependencies
RUN npm install
RUN cd shared && npm install
RUN cd errorhandler && npm install
RUN cd services/configservice && npm install

# Copy source code
COPY shared ./shared
COPY errorhandler ./errorhandler
COPY services/configservice ./services/configservice

# Build TypeScript
RUN cd shared && npm run build
RUN cd errorhandler && npm run build
RUN npm run build --workspace=services/configservice

# Create config directory
RUN mkdir -p /usr/src/app/config

# Expose port
EXPOSE 5090

# Start the service
CMD ["node", "services/configservice/dist/ConfigService.js"]
