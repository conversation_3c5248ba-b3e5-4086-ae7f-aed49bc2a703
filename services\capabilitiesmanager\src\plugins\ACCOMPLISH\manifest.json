{
  "id": "plugin-ACCOMPLISH",
  "verb": "ACCOMPLISH",
  "description": "Accomplishes a given goal or creates a plan to achieve it",
  "explanation": "This plugin takes a goal statement and either returns the result of accomplishing the goal or a plan of tasks to achieve it",
  "inputDefinitions": [
    {
      "name": "goal",
      "required": true,
      "type": "string",
      "description": "The goal to be accomplished or planned for"
    }
  ],
  "outputDefinitions": [
    {
      "name": "plan",
      "required": false,
      "type": "plan",
      "description": "A plan of tasks to achieve the goal, or a direct answer if the goal can be immediately accomplished"
    },
    {
      "name": "answer",
      "required": false,
      "type": "string",
      "description": "A solution that matches or achieves the goal"
    }
  ],
  "language": "javascript",
  "entryPoint": {
    "main": "ACCOMPLISH.js",
    "files": {
      "ACCOMPLISH.js": "const axios = require('axios');\n\n// --- Embedded Error Reporter Logic ---\nfunction generatePseudoUUID_embedded() {\n    let dt = new Date().getTime();\n    const uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n        const r = (dt + Math.random()*16)%16 | 0;\n        dt = Math.floor(dt/16);\n        return (c=='x' ? r :(r&0x3|0x8)).toString(16);\n    });\n    return uuid;\n}\n\nconst ErrorSeverity_EMBEDDED = {\n  INFO: 'INFO',\n  WARNING: 'WARNING',\n  ERROR: 'ERROR',\n  CRITICAL: 'CRITICAL',\n};\n\nconst PlanningErrorCodes_EMBEDDED = {\n  LLM_RESPONSE_PARSE_FAILED: 'P001_LLM_RESPONSE_PARSE_FAILED',\n  BRAIN_QUERY_FAILED: 'P002_BRAIN_QUERY_FAILED',\n  NO_GOAL_PROVIDED: 'P003_NO_GOAL_PROVIDED',\n  INVALID_BRAIN_RESPONSE_FORMAT: 'P004_INVALID_BRAIN_RESPONSE_FORMAT',\n  PLAN_VALIDATION_EMPTY_PLAN: 'P005_PLAN_VALIDATION_EMPTY_PLAN',\n  PLAN_VALIDATION_DUPLICATE_OUTPUT_NAME: 'P006_PLAN_VALIDATION_DUPLICATE_OUTPUT_NAME',\n  PLAN_VALIDATION_OUTPUT_NAME_CONVENTION_FAILED: 'P007_PLAN_VALIDATION_OUTPUT_NAME_CONVENTION_FAILED',\n  PLAN_VALIDATION_MISSING_OR_FORWARD_REF_INPUT: 'P008_PLAN_VALIDATION_MISSING_OR_FORWARD_REF_INPUT',\n  PLAN_VALIDATION_INVALID_STEP_STRUCTURE: 'P009_PLAN_VALIDATION_INVALID_STEP_STRUCTURE',\n  UNKNOWN_ACCOMPLISH_ERROR: 'P999_UNKNOWN_ACCOMPLISH_ERROR',\n};\n\nfunction generateStructuredError_embedded({\n  error_code,\n  severity,\n  message,\n  source_component,\n  contextual_info = {},\n  suggested_action,\n  original_error, // Expect an Error object or string\n  trace_id_param,\n}) {\n  const error_id = generatePseudoUUID_embedded();\n  const timestamp_utc = new Date().toISOString();\n  const trace_id = trace_id_param || generatePseudoUUID_embedded();\n\n  const enriched_contextual_info = { ...contextual_info };\n\n  if (original_error) {\n    if (typeof original_error === 'object' && original_error !== null && original_error.message) {\n        enriched_contextual_info.original_error_message = original_error.message;\n        if (original_error.stack) {\n            enriched_contextual_info.original_error_stack = original_error.stack;\n        }\n    } else {\n        enriched_contextual_info.original_error_details = String(original_error);\n    }\n  }\n  \n  const structuredError = {\n    error_id,\n    trace_id,\n    timestamp_utc,\n    error_code,\n    severity,\n    message_human_readable: message,\n    source_component,\n    contextual_info: enriched_contextual_info,\n  };\n\n  if (suggested_action) {\n    structuredError.suggested_action = suggested_action;\n  }\n  \n  console.error(`ACCOMPLISH Structured Error: ${JSON.stringify(structuredError, null, 2)}`);\n\n  return {\n      success: false,\n      name: error_code,\n      resultType: 'error',\n      resultDescription: message,\n      result: structuredError, \n      error: message, \n      mimeType: 'application/json'\n  };\n}\n// --- End of Embedded Error Reporter Logic ---\n\n// --- Embedded Plan Validator Logic ---\nfunction validatePlanStructure(planArray, trace_id_param) {\n    const validationErrors = [];\n    const source_component_validator = \"ACCOMPLISH_PLUGIN.validatePlanStructure\";\n\n    if (!planArray || !Array.isArray(planArray) || planArray.length === 0) {\n        validationErrors.push(generateStructuredError_embedded({\n            error_code: PlanningErrorCodes_EMBEDDED.PLAN_VALIDATION_EMPTY_PLAN,\n            severity: ErrorSeverity_EMBEDDED.ERROR,\n            message: \"LLM returned an empty or invalid plan structure (plan array is missing, not an array, or empty).\",\n            source_component: source_component_validator,\n            trace_id_param\n        }));\n        return validationErrors;\n    }\n\n    const allDeclaredOutputs = new Map(); // K: outputName, V: step.number where defined\n    const outputNameConventionRegex = /^[a-z]+(_[a-zA-Z0-9]+)+$/;\n\n    // Pass 1: Output Validation (Uniqueness and Convention)\n    planArray.forEach((step, index) => {\n        const stepNumber = step.number !== undefined ? step.number : (index + 1); \n\n        if (typeof step !== 'object' || step === null) {\n            validationErrors.push(generateStructuredError_embedded({\n                error_code: PlanningErrorCodes_EMBEDDED.PLAN_VALIDATION_INVALID_STEP_STRUCTURE,\n                severity: ErrorSeverity_EMBEDDED.ERROR,\n                message: `Plan step at index ${index} (number ${stepNumber}) is not a valid object.`,\n                source_component: source_component_validator,\n                trace_id_param,\n                contextual_info: { step_index: index, step_data_type: typeof step }\n            }));\n            return; \n        }\n\n        if (step.outputs !== undefined && (typeof step.outputs !== 'object' || step.outputs === null)) {\n            validationErrors.push(generateStructuredError_embedded({\n                error_code: PlanningErrorCodes_EMBEDDED.PLAN_VALIDATION_INVALID_STEP_STRUCTURE,\n                severity: ErrorSeverity_EMBEDDED.ERROR,\n                message: `Plan step ${stepNumber} has an invalid 'outputs' field; it must be an object if present.`, \n                source_component: source_component_validator,\n                trace_id_param,\n                contextual_info: { step_number: stepNumber, outputs_type: typeof step.outputs }\n            }));\n        } else if (step.outputs) {\n            for (const outputName in step.outputs) {\n                if (allDeclaredOutputs.has(outputName)) {\n                    validationErrors.push(generateStructuredError_embedded({\n                        error_code: PlanningErrorCodes_EMBEDDED.PLAN_VALIDATION_DUPLICATE_OUTPUT_NAME,\n                        severity: ErrorSeverity_EMBEDDED.ERROR,\n                        message: `Duplicate output name '${outputName}' found in step ${stepNumber}. Previously defined in step ${allDeclaredOutputs.get(outputName)}. Output names must be unique across the entire plan.`, \n                        source_component: source_component_validator,\n                        trace_id_param,\n                        contextual_info: { output_name: outputName, current_step: stepNumber, original_step: allDeclaredOutputs.get(outputName) }\n                    }));\n                } else {\n                    allDeclaredOutputs.set(outputName, stepNumber);\n                }\n\n                if (!outputNameConventionRegex.test(outputName)) {\n                    validationErrors.push(generateStructuredError_embedded({\n                        error_code: PlanningErrorCodes_EMBEDDED.PLAN_VALIDATION_OUTPUT_NAME_CONVENTION_FAILED,\n                        severity: ErrorSeverity_EMBEDDED.WARNING, \n                        message: `Output name '${outputName}' in step ${stepNumber} does not conform to the suggested naming convention (e.g., type_descriptor_qualifier, starting with lowercase letters). Example: 'dataset_userData_raw'.`, \n                        source_component: source_component_validator,\n                        trace_id_param,\n                        contextual_info: { output_name: outputName, step_number: stepNumber }\n                    }));\n                }\n            }\n        }\n    });\n\n    // Pass 2: Dependency Validation\n    const generatedOutputsSoFar = new Set();\n    planArray.forEach((step, index) => {\n        const stepNumber = step.number !== undefined ? step.number : (index + 1);\n        if (typeof step !== 'object' || step === null) return;\n\n        const stepArgs = step.args;\n        if (typeof stepArgs === 'object' && stepArgs !== null) {\n            for (const argKey in stepArgs) {\n                const argValue = stepArgs[argKey];\n                if (typeof argValue === 'string' && allDeclaredOutputs.has(argValue)) { \n                    if (!generatedOutputsSoFar.has(argValue)) {\n                        let isForwardRef = false;\n                        for (let k = index + 1; k < planArray.length; k++) {\n                            const futureStep = planArray[k];\n                            if (futureStep && typeof futureStep.outputs === 'object' && futureStep.outputs !== null && futureStep.outputs.hasOwnProperty(argValue)) {\n                                isForwardRef = true;\n                                break;\n                            }\n                        }\n                        validationErrors.push(generateStructuredError_embedded({\n                            error_code: PlanningErrorCodes_EMBEDDED.PLAN_VALIDATION_MISSING_OR_FORWARD_REF_INPUT,\n                            severity: ErrorSeverity_EMBEDDED.ERROR,\n                            message: `Input '${argValue}' for argument '${argKey}' in step ${stepNumber} is problematic. ${isForwardRef ? \"It's a forward reference to an output from a later step.\" : \"It refers to an output that is not generated by any preceding step or is out of order.\"} Inputs must refer to outputs from previously completed steps.`, \n                            source_component: source_component_validator,\n                            trace_id_param,\n                            contextual_info: { input_name: argValue, arg_key: argKey, step_number: stepNumber, is_forward_ref: isForwardRef }\n                        }));\n                    }\n                }\n            }\n        }\n        if (typeof step.outputs === 'object' && step.outputs !== null) {\n            for (const outputName in step.outputs) {\n                generatedOutputsSoFar.add(outputName);\n            }\n        }\n    });\n    return validationErrors;\n}\n// --- End of Embedded Plan Validator Logic ---\n\nasync function execute(input) {\n    const current_trace_id = generatePseudoUUID_embedded(); \n    try {\n        const goal = input.args?.goal || input.inputValue;\n        \n        if (!goal) {\n            console.log('Goal or description is required for ACCOMPLISH plugin');\n            return [generateStructuredError_embedded({\n                error_code: PlanningErrorCodes_EMBEDDED.NO_GOAL_PROVIDED,\n                severity: ErrorSeverity_EMBEDDED.ERROR,\n                message: 'Inputs did not contain a goal. Goal is required for ACCOMPLISH plugin.',\n                source_component: \"ACCOMPLISH_PLUGIN.execute\",\n                contextual_info: { \"received_input_keys\": input.args ? Object.keys(input.args) : (input.inputValue ? \"inputValue_present\" : \"no_input_args\") },\n                trace_id_param: current_trace_id\n            })];\n        }\n\n        const prompt = generatePrompt(goal);\n        const response = await queryBrain(prompt, current_trace_id);\n        \n        let parsedResponse;\n        try {\n            parsedResponse = JSON.parse(response);\n        } catch (parseError) {\n            let errorMessage = 'Failed to parse Brain response JSON.';\n            if (parseError instanceof Error) { errorMessage += `: ${parseError.message}`; }\n            return [generateStructuredError_embedded({\n                error_code: PlanningErrorCodes_EMBEDDED.LLM_RESPONSE_PARSE_FAILED,\n                severity: ErrorSeverity_EMBEDDED.ERROR,\n                message: errorMessage,\n                source_component: \"ACCOMPLISH_PLUGIN.execute\",\n                original_error: parseError,\n                contextual_info: { \"raw_response_snippet\": response ? response.substring(0, 500) : \"N/A\" },\n                trace_id_param: current_trace_id\n            })];\n        }\n\n        if (parsedResponse.type === 'PLAN') {\n            const validationErrors = validatePlanStructure(parsedResponse.plan, current_trace_id);\n            if (validationErrors && validationErrors.length > 0) {\n                return validationErrors; \n            }\n            const tasks = convertJsonToTasks(parsedResponse.plan);\n            return [{\n                success: true,\n                resultType: 'plan',\n                resultDescription: `A plan to: ${goal}`,\n                result: tasks,\n                plan_type: parsedResponse.plan_type || 'DETAILED_DIRECT_PLAN',\n                next_phase_guidance: parsedResponse.next_phase_guidance || null,\n                mimeType: 'application/json'\n            }];\n        } else if (parsedResponse.type === 'DIRECT_ANSWER') {\n            return [{\n                success: true,\n                resultType: 'string',\n                resultDescription: 'LLM Response',\n                result: parsedResponse.answer,\n                mimeType: 'text/plain'\n            }];\n        } else {\n            return [generateStructuredError_embedded({\n                error_code: PlanningErrorCodes_EMBEDDED.INVALID_BRAIN_RESPONSE_FORMAT,\n                severity: ErrorSeverity_EMBEDDED.ERROR,\n                message: 'Invalid response format from Brain: Missing or unknown type field in response.',\n                source_component: \"ACCOMPLISH_PLUGIN.execute\",\n                contextual_info: { \"parsed_response_type\": parsedResponse.type, \"raw_response_snippet\": response ? response.substring(0, 500) : \"N/A\" },\n                trace_id_param: current_trace_id\n            })];\n        }\n    \n    } catch (error) {\n        const isBrainQueryFailure = error && error.errorCode === PlanningErrorCodes_EMBEDDED.BRAIN_QUERY_FAILED;\n        return [generateStructuredError_embedded({\n            error_code: isBrainQueryFailure ? error.errorCode : PlanningErrorCodes_EMBEDDED.UNKNOWN_ACCOMPLISH_ERROR,\n            severity: ErrorSeverity_EMBEDDED.CRITICAL,\n            message: error instanceof Error ? error.message : 'An unknown error occurred in ACCOMPLISH plugin execution.',\n            source_component: \"ACCOMPLISH_PLUGIN.execute\",\n            original_error: error, \n            contextual_info: { \"goal\": (input.args?.goal || input.inputValue) },\n            trace_id_param: current_trace_id\n        })];\n    }\n}\n\nfunction generatePrompt(goal) {\n    const originalGoal = goal;\n    return `\nIMPORTANT: First, assess the user's goal: '${originalGoal}'.\nIf the goal is complex and best addressed in stages (e.g., \"Develop a new software module and deploy it\", \"Organize a multi-day conference\"), you MUST respond with a \"HIGH_LEVEL_PHASED_PLAN\".\nIf the goal is simple and can be fully planned in detail or answered directly, you MUST respond with a \"DIRECT_ANSWER\" or a \"DETAILED_DIRECT_PLAN\".\n\nIf you can provide a complete and direct answer or solution, respond with a JSON object in this format:\n{\n    \"type\": \"DIRECT_ANSWER\",\n    \"answer\": \"Your direct answer here\"\n}\n\nIf a plan is needed, it can be one of two types:\n\nTYPE 1: For simple goals - A DETAILED, DIRECT PLAN. Use this for goals that can be broken down into directly executable steps without needing high-level phases and user reviews in between.\n{\n    \"type\": \"PLAN\",\n    \"plan_type\": \"DETAILED_DIRECT_PLAN\",\n    \"plan\": [\n        {\n            \"number\": 1,\n            \"verb\": \"FETCH_API_DATA\",\n            \"description\": \"Fetches specific data from an API.\",\n            \"args\": { \"url\": \"https://api.example.com/simple_data\" },\n            \"dependencies\": [0],\n            \"outputs\": { \"json_simpleData_raw\": \"Raw JSON data from the API.\" },\n            \"potential_errors\": [{\"description\": \"API timeout.\", \"detection_method\": \"HTTP timeout\", \"recovery_strategy\": \"Retry once.\", \"impact_on_failure\": \"Critical\"}]\n        }\n    ]\n}\n\nTYPE 2: For complex goals - A HIGH-LEVEL PHASED PLAN. Use this for large projects requiring multiple stages, each of which might need its own detailed sub-plan later.\n{\n    \"type\": \"PLAN\",\n    \"plan_type\": \"HIGH_LEVEL_PHASED_PLAN\",\n    \"plan\": [\n        {\n            \"number\": 1,\n            \"verb\": \"DEFINE_PHASE\",\n            \"description\": \"Phase 1: Requirements Gathering and Analysis. [Requires Detailed Sub-Plan]\",\n            \"args\": {\n                \"custom_type\": \"phase_definition\",\n                \"phase_objective\": \"Clearly define the scope, requirements, and key deliverables for the project.\",\n                \"expected_outcomes\": [\"document_requirementsSpecification_v1\", \"list_userStories_v1\"]\n            },\n            \"dependencies\": [0],\n            \"outputs\": {\n                \"document_requirementsSpecification_v1\": \"The detailed requirements specification document.\",\n                \"list_userStories_v1\": \"A list of user stories derived from requirements.\"\n            },\n            \"potential_errors\": [\n                {\"description\": \"Stakeholder unavailability delays requirement gathering.\", \"detection_method\": \"Missed meetings or review deadlines.\", \"recovery_strategy\": \"Escalate to project sponsor; schedule buffer time.\", \"impact_on_failure\": \"Critical delay to project timeline.\"}\n            ]\n        },\n        {\n            \"number\": 2,\n            \"verb\": \"REVIEW_AND_DECIDE\",\n            \"description\": \"Review Phase 1 deliverables and decide on proceeding to Phase 2.\",\n            \"args\": {\n                \"custom_type\": \"review_point\",\n                \"review_inputs\": [\"document_requirementsSpecification_v1\", \"list_userStories_v1\"],\n                \"decision_prompt\": \"Have all requirements been captured and approved in 'document_requirementsSpecification_v1' and 'list_userStories_v1'? Is the project ready to move to the design phase?\"\n            },\n            \"dependencies\": [1],\n            \"outputs\": {\n                \"boolean_approval_toProceedToPhase2\": \"Decision status (true/false) for proceeding to Phase 2.\"\n            },\n            \"potential_errors\": []\n        }\n    ],\n    \"next_phase_guidance\": \"To generate a detailed plan for 'Phase 1: Requirements Gathering and Analysis', use the 'ACCOMPLISH' verb with the goal: 'Create a detailed sub-plan for Phase 1: Requirements Gathering and Analysis, using its objective (Clearly define the scope, requirements, and key deliverables for the project.) and expected outcomes (document_requirementsSpecification_v1, list_userStories_v1) as guidance. The overall project goal is: ${originalGoal}'.\"\n}\n\n\n**General Guidelines for Plan Creation (apply to both DETAILED_DIRECT_PLAN and sub-plans for phases):**\n\n1. Number each step sequentially, starting from 1 within its current plan array. Ensure 'number' field is present and correct.\n2. Use specific, actionable verbs for each step (e.g., SCRAPE, ANALYZE, PREDICT, FETCH_API_DATA, TRANSFORM_DATA, CONVERT_FORMAT, FILTER_RECORDS, or a specific plugin_id like 'weather-plugin-openapi-001'). For high-level phase plans, also use 'DEFINE_PHASE' and 'REVIEW_AND_DECIDE' as appropriate.\n3. Ensure each step has a clear, concise 'description' (string).\n4. Provide detailed arguments in an 'args' object for each step. If using an output from a previous step, use its exact name as an input value (string). For transformation verbs like TRANSFORM_DATA, args MUST include an 'input_data' field referencing a previous output and other arguments describing the transformation logic. For 'DEFINE_PHASE' and 'REVIEW_AND_DECIDE' verbs, see specific guidelines below.\n5. List 'dependencies' as an array of 'number' values from preceding steps. A step with 'number: N' can only depend on steps with 'number < N'. Use [0] if the step has no dependencies on other steps in this plan.\n\n6. **Define Step Outputs (VERY IMPORTANT for DETAILED_DIRECT_PLAN and sub-plans; for HIGH_LEVEL_PHASED_PLAN, outputs of DEFINE_PHASE steps represent phase deliverables):\n   a. 'outputs' MUST be an object if present. Each key is an output name, and its value is a brief human-readable description (string).\n   b. **Clarity is paramount:** Output names MUST be highly descriptive and unambiguous. Prioritize clarity over extreme brevity.\n   c. **Be Specific:** Avoid generic names. Instead of \`data\`, use \`dataset_rawSalesData_csv\` or \`list_cleanedUserProfiles_json\`.\n   d. **Uniqueness:** All output names within the entire plan (or sub-plan) MUST be unique across all steps.\n   e. **Structure:** Follow the pattern: \`[what_the_output_is]_[specific_descriptor_or_content]_[optional_qualifier]\` (e.g., 'dataset_customerProfiles_raw', 'list_errorMessages_validation', 'file_summaryReport_final', 'text_userFeedback_sanitized'). Output names must start with a lowercase letter or underscore, followed by alphanumeric characters and underscores.\n   f. **Examples of Good Output Names:** \`dataset_customerBehavior_raw\`, \`report_financialAnalysis_Q3_pdf\`, \`list_invalidEntries_dataQualityCheck\`, \`text_errorMessage_apiConnectionFailure\`, \`boolean_isUserAuthenticated_checkResult\`, \`json_apiResponse_userDetails\`, \`document_requirementsSpecification_v1\`.\n   g. **Self-Correction Check for Naming:** Before finalizing an output name, ask: \"If I see this name out of context, will I understand what it refers to?\" and \"Is this name easily distinguishable from ALL other output names in this plan?\"\n\n7. **Define Potential Error Handling for Each Step (IMPORTANT for DETAILED_DIRECT_PLAN and sub-plans; for HIGH_LEVEL_PHASED_PLAN, focus on phase-level risks for DEFINE_PHASE steps):\n   a. For each step, include a \`potential_errors\` array (can be empty if no specific errors anticipated beyond general execution failures).\n   b. Each element in this array should be an object describing a specific potential error and MUST include the following fields: \`description\` (string), \`detection_method\` (string), \`recovery_strategy\` (string), \`impact_on_failure\` (string).\n   c. Be thorough. Consider network issues, authentication, invalid inputs, unexpected outputs, resource limits.\n\n8. **Consider and Insert Explicit Data Transformation Steps (Primarily for DETAILED_DIRECT_PLAN and sub-plans):\n   a. After a step fetches or produces data, assess if it needs cleaning, filtering, reshaping, or format conversion for the next action.\n   b. If so, insert an explicit transformation step (e.g., \`TRANSFORM_DATA\`, \`CONVERT_FORMAT\`).\n   c. Ensure \`args\` for these steps clearly define \`input_data\`, \`transformation_logic_description\`, and other relevant parameters (e.g., \`output_format\`, \`selected_fields\`).\n   d. Outputs of transformation steps should be clearly named following Guideline #6.\n   e. Do not assume implicit transformations. Make them explicit steps.\n\n**Additional Guidelines for creating a HIGH_LEVEL_PHASED_PLAN:**\nI. **Identify Major Phases:** Break down the complex goal into 2-5 logical high-level phases.\nII. **Use 'DEFINE_PHASE' Verb:** Each phase MUST be introduced by a step with \`verb: \"DEFINE_PHASE\"\`.\n    - In its \`args\`, include \`\"custom_type\": \"phase_definition\"\`.\n    - In its \`args\`, define \`\"phase_objective\" (string): What this phase aims to achieve.\n    - In its \`args\`, define \`\"expected_outcomes\" (array of strings): Key deliverable names from this phase. These names MUST follow output naming convention (Guideline #6).\n    - The \`description\` for a DEFINE_PHASE step MUST include the marker \`[Requires Detailed Sub-Plan]\`.\n    - The \`outputs\` object of a DEFINE_PHASE step should list each string from \`expected_outcomes\` as a key, with a human-readable description as its value.\nIII. **Use 'REVIEW_AND_DECIDE' Verb:** After each `DEFINE_PHASE` step, typically include a step with `verb: "REVIEW_AND_DECIDE"`.\n    - In its `args`, include `\"custom_type\": \"review_point\"\`.\n    - In its `args`, list `\"review_inputs\" (array of strings): These should be output names from the preceding DEFINE_PHASE step(s).\n    - In its `args\`, provide a `\"decision_prompt\" (string): A question for user/system confirmation.\n    - The `output` of this step is typically a boolean indicating approval (e.g., `\"boolean_approval_forNextPhase\": \"...\"`) and MUST follow output naming conventions.\nIV. **Next Phase Guidance:** At the root of the `HIGH_LEVEL_PHASED_PLAN` response, include a `\"next_phase_guidance\"` string. This MUST instruct on how to request the detailed sub-plan for the *first* phase marked `[Requires Detailed Sub-Plan]`, referencing its objective and expected outcomes. Example: \"To generate a detailed plan for 'Phase 1...', use 'ACCOMPLISH' with goal: 'Create detailed sub-plan for Phase 1: [objective from phase 1] aiming for outcomes: [outcome1, outcome2]. Overall project goal: ${originalGoal}'.\"\n\n**Final Instructions (apply to all plan types):**\n- Aim for a reasonable number of steps appropriate to the plan type (3-7 for detailed, 2-5 phases for high-level).\n- Be thorough in ALL description fields.\n- Ensure the final step (of a detailed plan, or the final phase of a high-level plan) produces the desired outcome for the given goal.\n- The actionVerb DELEGATE is available to use to create sub-agents with goals of their own.\n\nEnsure your response is a valid JSON object.\n`;\n}\n\nasync function queryBrain(prompt, trace_id_for_query) {\n    try {\n        const brainUrl = process.env.BRAIN_URL || 'brain:5070';\n        const response = await axios.post(`http://${brainUrl}/chat`, {\n            exchanges: [{ role: 'user', message: prompt }],\n            optimization: 'accuracy'\n        });\n        return response.data.response;\n    } catch (error) {\n        const brainError = new Error('Failed to query Brain');\n        brainError.originalError = error; \n        brainError.errorCode = PlanningErrorCodes_EMBEDDED.BRAIN_QUERY_FAILED; \n        brainError.trace_id = trace_id_for_query; \n        throw brainError;\n    }\n}\n\nfunction convertJsonToTasks(jsonPlan) {\n    return jsonPlan.map(step => ({\n        verb: step.verb,\n        args: {\n            ...step.args,\n            description: step.description,\n            expectedOutputs: step.outputs\n        },\n        dependencies: step.dependencies,\n        potential_errors: step.potential_errors || [],\n        ...(step.args?.custom_type && { custom_type: step.args.custom_type })\n    }));\n}\n\nmodule.exports = { execute };"
    }
  },
  "repository": {
    "type": "local"
  },
  "security": {
    "permissions": [],
    "sandboxOptions": {},
    "trust": {
      "signature": "BHVEWkvZ+gqp7RUfvqtY6cwoY5k/g+FIuynYYSt6IGYI6VDaAl2dDJ/msTaX/T+ClDMXLgLt1tcQvXQjS9KIHr2K/JyXbLFLB6DHQtan5SLAQw6+994jetVQUU7Dc4VNah3HYHHmax3nXdpf3ag2TDcz7hxPsF7Jcd674jODfV3mZXqUJLN+tCU8AWaDXlv7KzdRhgFKPVnQDtx8bYqZELJz1WIEyeC++0Cj3gFGR5a4yUY6dddrPQL7H+mE97NlFH81hbFh/Um0BgZR+5Vf73IECICysXhuR6EBnSC1tSrK71AOm+m6aqNmRyr5a7RU9FjSH5MJq4lRAZH91bQw+w=="
    }
  },
  "distribution": {
    "downloads": 0,
    "rating": 0
  },
  "version": "1.0.0"
}