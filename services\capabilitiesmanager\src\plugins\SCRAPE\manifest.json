{"id": "plugin-SCRAPE", "verb": "SCRAPE", "description": "Scrapes content from a given URL", "explanation": "This plugin takes a URL and optional configuration to scrape specific content from a web page", "inputDefinitions": [{"name": "url", "required": true, "type": "string", "description": "The URL to scrape content from"}, {"name": "selector", "required": false, "type": "string", "description": "CSS selector to target specific elements (optional)"}, {"name": "attribute", "required": false, "type": "string", "description": "Attribute to extract from the selected elements (optional)"}, {"name": "limit", "required": false, "type": "number", "description": "Maximum number of results to return (optional)"}], "outputDefinitions": [{"name": "content", "required": false, "type": "array", "description": "Array of scraped content"}], "language": "javascript", "entryPoint": {"main": "SCRAPE.js", "files": {"SCRAPE.js": "const axios = require('axios');\nconst cheerio = require('cheerio');\n\nasync function execute(input) {\n    try {\n        const { url, selector, attribute, limit } = input.args;\n\n        if (!url) {\n            throw new Error('URL is required for SCRAPE plugin');\n        }\n\n        const response = await axios.get(url);\n        const $ = cheerio.load(response.data);\n        \n        let elements = selector ? $(selector) : $('body');\n        let results = [];\n        \n        elements.each((i, el) => {\n            if (limit && i >= limit) return false;\n            \n            let content;\n            if (attribute) {\n                content = $(el).attr(attribute);\n            } else {\n                content = $(el).text().trim();\n            }\n            \n            if (content) results.push(content);\n        });\n\n        return {\n            success: true,\n            resultType: 'array',\n            resultDescription: 'Scraped content',\n            result: results,\n            mimeType: 'application/json'\n        };\n    } catch (error) {\n        console.error('SCRAPE plugin failed:', error instanceof Error ? error.message : error);\n        return {\n            success: false,\n            resultType: 'error',\n            resultDescription: 'Error scraping content',\n            result: null,\n            error: error instanceof Error ? error.message : 'An unknown error occurred',\n            mimeType: 'text/plain'\n        };\n    }\n}\n\nmodule.exports = { execute };"}}, "repository": {"type": "local"}, "security": {"permissions": [], "sandboxOptions": {}, "trust": {"signature": "1v8qYLEKhIRhUnWubKeAv96AaOjVAePDNdFY07ZAWwr6ufp9AI4NQTdpsYGoSo6nAf2dJpQ9QbOYtThcsWyMnGE00tuSGyq6zzPrdMMxGub5OMHIICAYdfnpTT0yisj45OUrb/sUkSrjeCsDZFKg5iSiS5IUbhcoZnQr9xFqXpIurXX9kNLiBttmG8thw9WQtAVIpGW/bGncNKRGMd3r/IOSH/R0rl+Y9bgXWdIGtkOC9vMVKHIVPbVtpskAEHT9Rx9v40KKDT6r2EZfY7P22DVNq81s8nQaabXw2W71euqatzXaVND1snHmmZLka4Lx2cUxN4KUTIw/z+epVZ1jKw=="}}, "distribution": {"downloads": 0, "rating": 0}, "version": "1.0.0"}